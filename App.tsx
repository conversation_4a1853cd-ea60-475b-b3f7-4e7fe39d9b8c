import { useState } from 'react';
import { <PERSON><PERSON> } from "./components/ui/button";
import { Sidebar, SidebarContent, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarProvider, SidebarTrigger } from "./components/ui/sidebar";
import { Dashboard } from "./components/Dashboard";
import { UserManagement } from "./components/UserManagement";
import { CourseManagement } from "./components/CourseManagement";
import { Settings } from "./components/Settings";
import { LanguageProvider, useLanguage } from "./components/i18n/LanguageProvider";
import { LanguageSelector } from "./components/i18n/LanguageSelector";
import { 
  LayoutDashboard, 
  Users, 
  BookOpen, 
  BarChart3, 
  Settings as SettingsIcon,
  Menu,
  Bell,
  Search,
  X
} from 'lucide-react';
import { Input } from "./components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "./components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "./components/ui/dropdown-menu";

interface Tab {
  id: string;
  labelKey: string;
  icon: React.ComponentType<{ className?: string }>;
}

function AppContent() {
  const { t } = useLanguage();
  
  const menuItems = [
    { id: 'dashboard', labelKey: 'menu.dashboard', icon: LayoutDashboard },
    { id: 'users', labelKey: 'menu.users', icon: Users },
    { id: 'courses', labelKey: 'menu.courses', icon: BookOpen },
    { id: 'analytics', labelKey: 'menu.analytics', icon: BarChart3 },
    { id: 'settings', labelKey: 'menu.settings', icon: SettingsIcon }
  ];

  const [tabs, setTabs] = useState<Tab[]>([
    { id: 'dashboard', labelKey: 'menu.dashboard', icon: LayoutDashboard }
  ]);
  const [activeTab, setActiveTab] = useState('dashboard');

  const handleMenuClick = (menuItem: typeof menuItems[0]) => {
    const existingTab = tabs.find(tab => tab.id === menuItem.id);
    
    if (existingTab) {
      // 이미 열린 탭이면 해당 탭으로 이동
      setActiveTab(menuItem.id);
    } else {
      // 새 탭 추가 (최대 10개까지)
      if (tabs.length < 10) {
        setTabs(prev => [...prev, {
          id: menuItem.id,
          labelKey: menuItem.labelKey,
          icon: menuItem.icon
        }]);
        setActiveTab(menuItem.id);
      } else {
        // 10개 초과시 가장 왼쪽 탭 제거하고 새 탭 추가
        setTabs(prev => [
          ...prev.slice(1),
          {
            id: menuItem.id,
            labelKey: menuItem.labelKey,
            icon: menuItem.icon
          }
        ]);
        setActiveTab(menuItem.id);
      }
    }
  };

  const closeTab = (tabId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (tabs.length === 1) {
      // 마지막 탭은 닫을 수 없음
      return;
    }

    const tabIndex = tabs.findIndex(tab => tab.id === tabId);
    const newTabs = tabs.filter(tab => tab.id !== tabId);
    
    setTabs(newTabs);

    // 닫힌 탭이 활성 탭이었으면 다른 탭으로 이동
    if (activeTab === tabId) {
      if (tabIndex === 0) {
        // 첫 번째 탭이 닫혔으면 다음 탭으로
        setActiveTab(newTabs[0].id);
      } else {
        // 그 외에는 이전 탭으로
        setActiveTab(newTabs[tabIndex - 1].id);
      }
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'users':
        return <UserManagement />;
      case 'courses':
        return <CourseManagement />;
      case 'analytics':
        return <Dashboard />; // Using Dashboard for analytics as they share similar components
      case 'settings':
        return <Settings />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <Sidebar>
          <SidebarHeader className="border-b px-6 py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-bold">{t('platform.logo')}</span>
              </div>
              <span className="font-semibold">{t('platform.name')}</span>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    isActive={activeTab === item.id}
                    onClick={() => handleMenuClick(item)}
                    className="w-full justify-start"
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    {t(item.labelKey)}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarContent>
        </Sidebar>

        <div className="flex-1 flex flex-col">
          {/* Header */}
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-16 items-center px-6">
              <SidebarTrigger className="mr-4 md:hidden" />
              
              <div className="flex items-center space-x-4 flex-1">
                <div className="relative max-w-sm flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    placeholder={t('header.search')}
                    className="pl-8 w-full"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <LanguageSelector />
                
                <Button variant="ghost" size="icon">
                  <Bell className="h-4 w-4" />
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="https://api.dicebear.com/7.x/avataaars/svg?seed=admin" />
                        <AvatarFallback>{t('platform.logo')}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{t('header.admin')}</p>
                        <p className="text-xs leading-none text-muted-foreground">
                          <EMAIL>
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>{t('header.profile')}</DropdownMenuItem>
                    <DropdownMenuItem>{t('header.billing')}</DropdownMenuItem>
                    <DropdownMenuItem>{t('header.team')}</DropdownMenuItem>
                    <DropdownMenuItem>{t('header.subscription')}</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>{t('header.logout')}</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </header>

          {/* Tab Bar */}
          <div className="border-b bg-background">
            <div className="flex items-center px-6">
              <div className="flex items-center space-x-1 overflow-x-auto">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <div
                      key={tab.id}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-t-lg cursor-pointer border-b-2 transition-colors min-w-0 ${
                        activeTab === tab.id
                          ? 'bg-muted border-primary text-primary'
                          : 'border-transparent hover:bg-muted/50'
                      }`}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <IconComponent className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm truncate max-w-32">{t(tab.labelKey)}</span>
                      {tabs.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 hover:bg-muted-foreground/20 flex-shrink-0"
                          onClick={(e) => closeTab(tab.id, e)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
              
              {tabs.length >= 10 && (
                <div className="ml-4 text-xs text-muted-foreground">
                  {t('header.maxTabs')}
                </div>
              )}
            </div>
          </div>

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-6">
            {renderContent()}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}

export default function App() {
  return (
    <LanguageProvider>
      <AppContent />
    </LanguageProvider>
  );
}