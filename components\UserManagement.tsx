import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./ui/table";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "./ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "./ui/dropdown-menu";
import { Search, Plus, MoreHorizontal, Mail, Phone } from 'lucide-react';
import { useLanguage } from './i18n/LanguageProvider';

export function UserManagement() {
  const { t, language } = useLanguage();

  const students = [
    {
      id: 1,
      name: language === 'ko' ? "김수영" : "<PERSON>",
      email: language === 'ko' ? "<EMAIL>" : "<EMAIL>",
      enrolledCourses: 3,
      completedCourses: 1,
      status: "active",
      joinDate: "2024-01-15"
    },
    {
      id: 2,
      name: language === 'ko' ? "이민호" : "Bob Smith",
      email: language === 'ko' ? "<EMAIL>" : "<EMAIL>",
      enrolledCourses: 2,
      completedCourses: 2,
      status: "active",
      joinDate: "2024-02-20"
    },
    {
      id: 3,
      name: language === 'ko' ? "박지은" : "Carol Davis",
      email: language === 'ko' ? "<EMAIL>" : "<EMAIL>",
      enrolledCourses: 1,
      completedCourses: 0,
      status: "inactive",
      joinDate: "2024-03-10"
    },
    {
      id: 4,
      name: language === 'ko' ? "정우성" : "David Wilson",
      email: language === 'ko' ? "<EMAIL>" : "<EMAIL>",
      enrolledCourses: 4,
      completedCourses: 3,
      status: "active",
      joinDate: "2024-01-05"
    }
  ];

  const instructors = [
    {
      id: 1,
      name: language === 'ko' ? "김영훈 교수" : "Dr. Sarah Miller",
      email: language === 'ko' ? "<EMAIL>" : "<EMAIL>",
      courses: 3,
      students: 145,
      rating: 4.8,
      status: "active",
      expertise: t('category.webDevelopment')
    },
    {
      id: 2,
      name: language === 'ko' ? "이수진 박사" : "Prof. Michael Chen",
      email: language === 'ko' ? "<EMAIL>" : "<EMAIL>",
      courses: 2,
      students: 89,
      rating: 4.9,
      status: "active",
      expertise: t('category.datascience')
    },
    {
      id: 3,
      name: language === 'ko' ? "박현우 교수" : "Dr. Emma Thompson",
      email: language === 'ko' ? "<EMAIL>" : "<EMAIL>",
      courses: 4,
      students: 203,
      rating: 4.7,
      status: "active",
      expertise: t('category.uiux')
    }
  ];

  const formatCourseCount = (count: number) => {
    if (language === 'ko') {
      return `${count}개`;
    }
    return count.toString();
  };

  const formatStudentCount = (count: number) => {
    if (language === 'ko') {
      return `${count}명`;
    }
    return count.toString();
  };

  return (
    <div className="space-y-6">
      <div>
        <h1>{t('users.title')}</h1>
        <p className="text-muted-foreground">
          {t('users.description')}
        </p>
      </div>

      <Tabs defaultValue="students" className="space-y-4">
        <TabsList>
          <TabsTrigger value="students">{t('users.students')}</TabsTrigger>
          <TabsTrigger value="instructors">{t('users.instructors')}</TabsTrigger>
        </TabsList>

        <TabsContent value="students" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{t('users.students')}</CardTitle>
                  <CardDescription>
                    {t('users.studentsDescription')}
                  </CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('users.addStudent')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder={t('users.searchStudents')} className="pl-8" />
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('users.students')}</TableHead>
                    <TableHead>{t('users.enrolledCourses')}</TableHead>
                    <TableHead>{t('users.completedCourses')}</TableHead>
                    <TableHead>{t('users.status')}</TableHead>
                    <TableHead>{t('users.joinDate')}</TableHead>
                    <TableHead className="w-[70px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {students.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${student.name}`} />
                            <AvatarFallback>{student.name.substring(0, 2)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{student.name}</p>
                            <p className="text-sm text-muted-foreground">{student.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCourseCount(student.enrolledCourses)}</TableCell>
                      <TableCell>{formatCourseCount(student.completedCourses)}</TableCell>
                      <TableCell>
                        <Badge variant={student.status === 'active' ? 'default' : 'secondary'}>
                          {student.status === 'active' ? t('users.active') : t('users.inactive')}
                        </Badge>
                      </TableCell>
                      <TableCell>{student.joinDate}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>{t('users.actions')}</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              {t('users.sendMessage')}
                            </DropdownMenuItem>
                            <DropdownMenuItem>{t('users.viewProfile')}</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              {t('users.suspendUser')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="instructors" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{t('users.instructors')}</CardTitle>
                  <CardDescription>
                    {t('users.instructorsDescription')}
                  </CardDescription>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('users.addInstructor')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder={t('users.searchInstructors')} className="pl-8" />
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('users.instructors')}</TableHead>
                    <TableHead>{t('courses.allCourses')}</TableHead>
                    <TableHead>{t('users.students')}</TableHead>
                    <TableHead>{t('users.rating')}</TableHead>
                    <TableHead>{t('users.expertise')}</TableHead>
                    <TableHead className="w-[70px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {instructors.map((instructor) => (
                    <TableRow key={instructor.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${instructor.name}`} />
                            <AvatarFallback>{instructor.name.substring(0, 2)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{instructor.name}</p>
                            <p className="text-sm text-muted-foreground">{instructor.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatCourseCount(instructor.courses)}</TableCell>
                      <TableCell>{formatStudentCount(instructor.students)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <span className="mr-1">⭐</span>
                          {instructor.rating}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{instructor.expertise}</Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>{t('users.actions')}</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              {t('users.sendMessage')}
                            </DropdownMenuItem>
                            <DropdownMenuItem>{t('users.viewProfile')}</DropdownMenuItem>
                            <DropdownMenuItem>{t('users.assignCourse')}</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              {t('users.removeInstructor')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}