/* Custom styles for the LMS Admin Panel */
:root {
    --font-size: 14px;
    --font-weight-medium: 500;
    --font-weight-normal: 400;
}

html {
    font-size: var(--font-size);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.5;
}

/* Sidebar Styles */
.sidebar-menu-item {
    @apply w-full flex items-center px-3 py-2 text-sm text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md transition-colors duration-200;
}

.sidebar-menu-item.active {
    @apply bg-sidebar-primary text-sidebar-primary-foreground;
}

/* Tab Styles */
.tab {
    @apply flex items-center space-x-2 px-3 py-2 rounded-t-lg cursor-pointer border-b-2 transition-colors min-w-0;
}

.tab.active {
    @apply bg-muted border-primary text-primary;
}

.tab:not(.active) {
    @apply border-transparent hover:bg-muted/50;
}

.tab-close {
    @apply h-4 w-4 p-0 hover:bg-muted-foreground/20 flex-shrink-0 ml-2 rounded opacity-0 group-hover:opacity-100 transition-opacity;
}

/* Page Content */
.page-content {
    @apply hidden;
}

.page-content.active {
    @apply block;
}

/* User Tab Styles */
.user-tab {
    @apply py-4 border-b-2 transition-colors duration-200 cursor-pointer;
}

.user-tab.active {
    @apply border-primary text-primary font-medium;
}

.user-tab:not(.active) {
    @apply border-transparent hover:text-primary;
}

.user-tab-content {
    @apply hidden;
}

.user-tab-content.active {
    @apply block;
}

/* Settings Tab Styles */
.settings-tab {
    @apply py-4 border-b-2 transition-colors duration-200 cursor-pointer;
}

.settings-tab.active {
    @apply border-primary text-primary font-medium;
}

.settings-tab:not(.active) {
    @apply border-transparent hover:text-primary;
}

/* Dropdown Styles */
.dropdown-menu {
    @apply absolute right-0 top-full mt-1 bg-popover border border-border rounded-md shadow-md z-50;
}

/* Badge Styles */
.badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
}

.badge-default {
    @apply bg-primary text-primary-foreground;
}

.badge-secondary {
    @apply bg-secondary text-secondary-foreground;
}

.badge-outline {
    @apply border border-border bg-background text-foreground;
}

/* Progress Bar */
.progress {
    @apply w-full bg-secondary rounded-full overflow-hidden;
}

.progress-bar {
    @apply h-full bg-primary transition-all duration-300 ease-in-out;
}

/* Table Styles */
table {
    @apply w-full border-collapse;
}

th {
    @apply text-left py-3 px-4 font-medium border-b border-border;
}

td {
    @apply py-3 px-4 border-b border-border;
}

tbody tr:hover {
    @apply bg-muted/50;
}

/* Avatar Styles */
.avatar {
    @apply relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full;
}

.avatar img {
    @apply aspect-square h-full w-full object-cover;
}

/* Course Card Styles */
.course-card {
    @apply bg-card border border-border rounded-lg overflow-hidden;
}

.course-thumbnail {
    @apply w-48 h-32 bg-muted flex-shrink-0;
}

.course-thumbnail img {
    @apply w-full h-full object-cover;
}

/* Responsive */
@media (max-width: 768px) {
    #sidebar {
        @apply fixed inset-y-0 left-0 z-50 transform -translate-x-full transition-transform duration-300;
    }
    
    #sidebar.open {
        @apply translate-x-0;
    }
    
    .main-content {
        @apply ml-0;
    }
    
    #sidebar-overlay {
        @apply fixed inset-0 bg-black/50 z-40;
    }
}

/* Typography */
h1 {
    font-size: 1.875rem;
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

h2 {
    font-size: 1.5rem;
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
}

h3 {
    font-size: 1.25rem;
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
}

h4 {
    font-size: 1rem;
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
}

p {
    font-size: 1rem;
    font-weight: var(--font-weight-normal);
    line-height: 1.5;
}

/* Dark mode support */
.dark {
    --background: #0a0a0a;
    --foreground: #fafafa;
    --card: #0a0a0a;
    --card-foreground: #fafafa;
    --popover: #0a0a0a;
    --popover-foreground: #fafafa;
    --primary: #fafafa;
    --primary-foreground: #1a1a1a;
    --secondary: #262626;
    --secondary-foreground: #fafafa;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --accent: #262626;
    --accent-foreground: #fafafa;
    --destructive: #dc2626;
    --destructive-foreground: #fafafa;
    --border: #262626;
    --input: #262626;
    --ring: #737373;
    --sidebar: #1a1a1a;
    --sidebar-foreground: #fafafa;
    --sidebar-primary: #3b82f6;
    --sidebar-primary-foreground: #fafafa;
    --sidebar-accent: #262626;
    --sidebar-accent-foreground: #fafafa;
    --sidebar-border: #262626;
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-down {
    animation: slideDown 0.2s ease-in-out;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* Focus styles */
input:focus,
button:focus,
select:focus {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
}

/* Loading spinner */
.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #030213;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}