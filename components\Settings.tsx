import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Switch } from "./ui/switch";
import { Textarea } from "./ui/textarea";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Separator } from "./ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Badge } from "./ui/badge";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { useLanguage } from './i18n/LanguageProvider';

export function Settings() {
  const { t, language } = useLanguage();

  const getDefaultPlatformName = () => {
    return language === 'ko' ? '에듀런 LMS' : 'EduLearn LMS';
  };

  const getDefaultPlatformDescription = () => {
    return language === 'ko' 
      ? '현대적 교육을 위한 종합 학습 관리 시스템'
      : 'A comprehensive learning management system for modern education';
  };

  const getDefaultPhone = () => {
    return language === 'ko' ? '02-1234-5678' : '+****************';
  };

  const getDefaultNotificationEmail = () => {
    return language === 'ko' ? '<EMAIL>' : '<EMAIL>';
  };

  return (
    <div className="space-y-6">
      <div>
        <h1>{t('settings.title')}</h1>
        <p className="text-muted-foreground">
          {t('settings.description')}
        </p>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">{t('settings.general')}</TabsTrigger>
          <TabsTrigger value="notifications">{t('settings.notifications')}</TabsTrigger>
          <TabsTrigger value="security">{t('settings.security')}</TabsTrigger>
          <TabsTrigger value="integrations">{t('settings.integrations')}</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.platformInfo')}</CardTitle>
              <CardDescription>
                {t('settings.platformInfoDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="platform-name">{t('settings.platformName')}</Label>
                <Input id="platform-name" defaultValue={getDefaultPlatformName()} />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="platform-description">{t('settings.platformDescription')}</Label>
                <Textarea 
                  id="platform-description" 
                  defaultValue={getDefaultPlatformDescription()}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="platform-logo">{t('settings.platformLogo')}</Label>
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
                    <ImageWithFallback 
                      src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=64&h=64&fit=crop"
                      alt={t('settings.platformLogo')}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  </div>
                  <Button variant="outline">{t('settings.changeLogo')}</Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contact-email">{t('settings.contactEmail')}</Label>
                  <Input id="contact-email" type="email" defaultValue="<EMAIL>" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="support-phone">{t('settings.supportPhone')}</Label>
                  <Input id="support-phone" defaultValue={getDefaultPhone()} />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timezone">{t('settings.timezone')}</Label>
                <Select defaultValue={language === 'ko' ? 'korea' : 'utc-5'}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {language === 'ko' ? (
                      <>
                        <SelectItem value="korea">한국 표준시 (KST)</SelectItem>
                        <SelectItem value="japan">일본 표준시 (JST)</SelectItem>
                        <SelectItem value="china">중국 표준시 (CST)</SelectItem>
                        <SelectItem value="utc">협정 세계시 (UTC)</SelectItem>
                      </>
                    ) : (
                      <>
                        <SelectItem value="utc-8">Pacific Time (UTC-8)</SelectItem>
                        <SelectItem value="utc-7">Mountain Time (UTC-7)</SelectItem>
                        <SelectItem value="utc-6">Central Time (UTC-6)</SelectItem>
                        <SelectItem value="utc-5">Eastern Time (UTC-5)</SelectItem>
                        <SelectItem value="utc">UTC</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <Button>{t('settings.saveChanges')}</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.emailNotifications')}</CardTitle>
              <CardDescription>
                {t('settings.emailNotificationsDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('settings.newStudentRegistration')}</Label>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.newStudentDescription')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('settings.courseCompletion')}</Label>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.courseCompletionDescription')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('settings.paymentReceived')}</Label>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.paymentDescription')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('settings.systemMaintenance')}</Label>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.maintenanceDescription')}
                  </p>
                </div>
                <Switch />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="notification-email">{t('settings.notificationEmail')}</Label>
                <Input id="notification-email" type="email" defaultValue={getDefaultNotificationEmail()} />
              </div>

              <Button>{t('settings.savePreferences')}</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.securitySettings')}</CardTitle>
              <CardDescription>
                {t('settings.securityDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('settings.twoFactorAuth')}</Label>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.twoFactorDescription')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('settings.passwordRequirements')}</Label>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.passwordDescription')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="space-y-2">
                <Label htmlFor="session-timeout">{t('settings.sessionTimeout')}</Label>
                <Select defaultValue="60">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {language === 'ko' ? (
                      <>
                        <SelectItem value="30">30분</SelectItem>
                        <SelectItem value="60">60분</SelectItem>
                        <SelectItem value="120">2시간</SelectItem>
                        <SelectItem value="240">4시간</SelectItem>
                      </>
                    ) : (
                      <>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">60 minutes</SelectItem>
                        <SelectItem value="120">2 hours</SelectItem>
                        <SelectItem value="240">4 hours</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('settings.loginAttemptLimit')}</Label>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.loginAttemptDescription')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <Button>{t('settings.updateSecurity')}</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.thirdPartyIntegrations')}</CardTitle>
              <CardDescription>
                {t('settings.integrationsDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      📧
                    </div>
                    <div>
                      <h4>{t('settings.emailProvider')}</h4>
                      <p className="text-sm text-muted-foreground">{t('settings.emailProviderDescription')}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="default">{t('settings.connected')}</Badge>
                    <Button variant="outline" size="sm">{t('settings.configure')}</Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      💳
                    </div>
                    <div>
                      <h4>{t('settings.paymentGateway')}</h4>
                      <p className="text-sm text-muted-foreground">{t('settings.paymentGatewayDescription')}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="default">{t('settings.connected')}</Badge>
                    <Button variant="outline" size="sm">{t('settings.configure')}</Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      📊
                    </div>
                    <div>
                      <h4>{t('settings.analytics')}</h4>
                      <p className="text-sm text-muted-foreground">{t('settings.analyticsDescription')}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{t('settings.notConnected')}</Badge>
                    <Button variant="outline" size="sm">{t('settings.connect')}</Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      🎥
                    </div>
                    <div>
                      <h4>{t('settings.videoHosting')}</h4>
                      <p className="text-sm text-muted-foreground">{t('settings.videoHostingDescription')}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{t('settings.notConnected')}</Badge>
                    <Button variant="outline" size="sm">{t('settings.connect')}</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}