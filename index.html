<!DOCTYPE html>
<html lang="ko" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>에듀런 LMS 관리자</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        border: 'rgba(0, 0, 0, 0.1)',
                        input: 'transparent',
                        ring: '#b8bcc8',
                        background: '#ffffff',
                        foreground: '#030213',
                        primary: '#030213',
                        'primary-foreground': '#ffffff',
                        secondary: '#f1f2f4',
                        'secondary-foreground': '#030213',
                        destructive: '#d4183d',
                        'destructive-foreground': '#ffffff',
                        muted: '#ececf0',
                        'muted-foreground': '#717182',
                        accent: '#e9ebef',
                        'accent-foreground': '#030213',
                        popover: '#ffffff',
                        'popover-foreground': '#030213',
                        card: '#ffffff',
                        'card-foreground': '#030213',
                        sidebar: '#fafafa',
                        'sidebar-foreground': '#030213',
                        'sidebar-primary': '#030213',
                        'sidebar-primary-foreground': '#fafafa',
                        'sidebar-accent': '#f5f5f5',
                        'sidebar-accent-foreground': '#1a1a1a',
                        'sidebar-border': '#e7e7e7'
                    },
                    fontSize: {
                        'base': '14px'
                    },
                    borderRadius: {
                        'lg': '0.625rem',
                        'md': 'calc(0.625rem - 2px)',
                        'sm': 'calc(0.625rem - 4px)'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-background text-foreground">
    <div id="app" class="min-h-screen flex w-full bg-background">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 bg-sidebar border-r border-sidebar-border flex flex-col">
            <!-- Sidebar Header -->
            <div class="border-b border-sidebar-border px-6 py-4">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                        <span class="text-primary-foreground text-sm font-medium" id="platform-logo">에</span>
                    </div>
                    <span class="font-semibold text-sidebar-foreground" id="platform-name">에듀런 LMS</span>
                </div>
            </div>
            
            <!-- Sidebar Menu -->
            <div class="flex-1 p-4">
                <nav class="space-y-1">
                    <button class="sidebar-menu-item active" data-page="dashboard">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <rect x="3" y="3" width="7" height="7"/><rect x="14" y="3" width="7" height="7"/><rect x="14" y="14" width="7" height="7"/><rect x="3" y="14" width="7" height="7"/>
                        </svg>
                        <span class="menu-text" data-i18n="menu.dashboard">대시보드</span>
                    </button>
                    <button class="sidebar-menu-item" data-page="users">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="m16 21-5.5-9L16 3"></path><path d="m21 21-5.5-9L21 3"></path><path d="m6 21-5.5-9L6 3"></path>
                        </svg>
                        <span class="menu-text" data-i18n="menu.users">사용자 관리</span>
                    </button>
                    <button class="sidebar-menu-item" data-page="courses">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/>
                        </svg>
                        <span class="menu-text" data-i18n="menu.courses">강의 관리</span>
                    </button>
                    <button class="sidebar-menu-item" data-page="analytics">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M3 3v18h18"/><path d="m19 9-5 5-4-4-3 3"/>
                        </svg>
                        <span class="menu-text" data-i18n="menu.analytics">분석</span>
                    </button>
                    <button class="sidebar-menu-item" data-page="settings">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/>
                        </svg>
                        <span class="menu-text" data-i18n="menu.settings">설정</span>
                    </button>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <header class="border-b bg-background/95 backdrop-blur">
                <div class="flex h-16 items-center px-6">
                    <button id="sidebar-toggle" class="mr-4 md:hidden p-2 hover:bg-accent rounded-md">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="18" x2="21" y2="18"></line>
                        </svg>
                    </button>
                    
                    <div class="flex items-center space-x-4 flex-1">
                        <div class="relative max-w-sm flex-1">
                            <svg class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path>
                            </svg>
                            <input type="text" placeholder="검색..." class="pl-8 w-full h-9 px-3 py-1 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring" data-i18n-placeholder="header.search">
                        </div>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Language Selector -->
                        <div class="relative">
                            <button id="language-selector" class="p-2 hover:bg-accent rounded-md">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                                </svg>
                            </button>
                            <div id="language-menu" class="absolute right-0 top-full mt-1 w-40 bg-popover border border-border rounded-md shadow-md hidden z-50">
                                <button class="language-option w-full px-3 py-2 text-left hover:bg-accent flex items-center" data-lang="ko">
                                    <span class="mr-2">🇰🇷</span> 한국어
                                </button>
                                <button class="language-option w-full px-3 py-2 text-left hover:bg-accent flex items-center" data-lang="en">
                                    <span class="mr-2">🇺🇸</span> English
                                </button>
                            </div>
                        </div>

                        <!-- Notifications -->
                        <button class="p-2 hover:bg-accent rounded-md">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                            </svg>
                        </button>
                        
                        <!-- User Menu -->
                        <div class="relative">
                            <button id="user-menu-trigger" class="relative h-8 w-8 rounded-full bg-accent">
                                <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=admin" alt="Admin" class="h-8 w-8 rounded-full">
                            </button>
                            <div id="user-menu" class="absolute right-0 top-full mt-1 w-56 bg-popover border border-border rounded-md shadow-md hidden z-50">
                                <div class="px-3 py-2 border-b">
                                    <p class="text-sm font-medium" data-i18n="header.admin">관리자</p>
                                    <p class="text-xs text-muted-foreground"><EMAIL></p>
                                </div>
                                <button class="w-full px-3 py-2 text-left hover:bg-accent" data-i18n="header.profile">프로필</button>
                                <button class="w-full px-3 py-2 text-left hover:bg-accent" data-i18n="header.billing">결제</button>
                                <button class="w-full px-3 py-2 text-left hover:bg-accent" data-i18n="header.team">팀</button>
                                <button class="w-full px-3 py-2 text-left hover:bg-accent" data-i18n="header.subscription">구독</button>
                                <div class="border-t">
                                    <button class="w-full px-3 py-2 text-left hover:bg-accent" data-i18n="header.logout">로그아웃</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Tab Bar -->
            <div class="border-b bg-background">
                <div class="flex items-center px-6">
                    <div id="tab-container" class="flex items-center space-x-1 overflow-x-auto">
                        <!-- Tabs will be dynamically inserted here -->
                    </div>
                    <div id="max-tabs-indicator" class="ml-4 text-xs text-muted-foreground hidden" data-i18n="header.maxTabs">최대 10개 탭</div>
                </div>
            </div>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-auto p-6">
                <!-- Dashboard Page -->
                <div id="page-dashboard" class="page-content active">
                    <div class="space-y-6">
                        <div>
                            <h1 data-i18n="dashboard.title">대시보드</h1>
                            <p class="text-muted-foreground" data-i18n="dashboard.welcome">
                                LMS 관리자 대시보드에 오신 것을 환영합니다. 플랫폼 현황을 한눈에 확인하세요.
                            </p>
                        </div>

                        <!-- Stats Cards -->
                        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div class="bg-card border border-border rounded-lg p-6">
                                <div class="flex items-center justify-between pb-2">
                                    <h3 class="text-sm font-medium" data-i18n="dashboard.totalStudents">전체 학생</h3>
                                    <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path d="m16 21-5.5-9L16 3"></path><path d="m21 21-5.5-9L21 3"></path><path d="m6 21-5.5-9L6 3"></path>
                                    </svg>
                                </div>
                                <div class="text-2xl font-bold" id="total-students">2,847명</div>
                                <p class="text-xs text-muted-foreground" data-i18n="dashboard.lastMonthIncrease">지난달 대비 +12%</p>
                            </div>

                            <div class="bg-card border border-border rounded-lg p-6">
                                <div class="flex items-center justify-between pb-2">
                                    <h3 class="text-sm font-medium" data-i18n="dashboard.activeCourses">활성 강의</h3>
                                    <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/>
                                    </svg>
                                </div>
                                <div class="text-2xl font-bold" id="active-courses">127개</div>
                                <p class="text-xs text-muted-foreground" data-i18n="dashboard.newThisWeek">이번주 +3개 신규</p>
                            </div>

                            <div class="bg-card border border-border rounded-lg p-6">
                                <div class="flex items-center justify-between pb-2">
                                    <h3 class="text-sm font-medium" data-i18n="dashboard.instructors">강사</h3>
                                    <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <circle cx="12" cy="12" r="3"></circle><path d="m12 1 2 5h5l-4 3 1.5 4.5L12 11l-4.5 2.5L9 9l-4-3h5l2-5Z"></path>
                                    </svg>
                                </div>
                                <div class="text-2xl font-bold" id="total-instructors">89명</div>
                                <p class="text-xs text-muted-foreground" data-i18n="dashboard.joinedThisMonth">이번달 +2명 가입</p>
                            </div>

                            <div class="bg-card border border-border rounded-lg p-6">
                                <div class="flex items-center justify-between pb-2">
                                    <h3 class="text-sm font-medium" data-i18n="dashboard.revenue">수익</h3>
                                    <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path d="m3 17 6-6 4 4 8-8"/><path d="m14 7 3-3 3 3"/><path d="M17 4v6"/>
                                    </svg>
                                </div>
                                <div class="text-2xl font-bold" id="total-revenue">₩54,231,000</div>
                                <p class="text-xs text-muted-foreground" data-i18n="dashboard.lastMonthRevenue">지난달 대비 +18%</p>
                            </div>
                        </div>

                        <!-- Charts -->
                        <div class="grid gap-4 md:grid-cols-2">
                            <div class="bg-card border border-border rounded-lg p-6">
                                <h3 class="font-semibold mb-2" data-i18n="dashboard.enrollmentTrend">학생 등록 추이</h3>
                                <p class="text-sm text-muted-foreground mb-4" data-i18n="dashboard.enrollmentDescription">최근 6개월간 월별 학생 등록 현황</p>
                                <canvas id="enrollmentChart" width="400" height="300"></canvas>
                            </div>

                            <div class="bg-card border border-border rounded-lg p-6">
                                <h3 class="font-semibold mb-2" data-i18n="dashboard.completionRates">강의 완료율</h3>
                                <p class="text-sm text-muted-foreground mb-4" data-i18n="dashboard.completionDescription">각 강의별 학생 완료 비율</p>
                                <canvas id="completionChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Page -->
                <div id="page-users" class="page-content">
                    <div class="space-y-6">
                        <div>
                            <h1 data-i18n="users.title">사용자 관리</h1>
                            <p class="text-muted-foreground" data-i18n="users.description">플랫폼의 학생과 강사를 관리합니다.</p>
                        </div>

                        <!-- Tab Navigation -->
                        <div class="bg-card border border-border rounded-lg">
                            <div class="border-b">
                                <nav class="flex space-x-8 px-6">
                                    <button class="user-tab active py-4 border-b-2 border-primary text-primary font-medium" data-tab="students">
                                        <span data-i18n="users.students">학생</span>
                                    </button>
                                    <button class="user-tab py-4 border-b-2 border-transparent hover:text-primary" data-tab="instructors">
                                        <span data-i18n="users.instructors">강사</span>
                                    </button>
                                </nav>
                            </div>

                            <!-- Students Tab -->
                            <div id="students-content" class="user-tab-content active p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="font-semibold" data-i18n="users.students">학생</h3>
                                        <p class="text-sm text-muted-foreground" data-i18n="users.studentsDescription">학생 계정과 수강 현황을 관리합니다</p>
                                    </div>
                                    <button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">
                                        <span data-i18n="users.addStudent">학생 추가</span>
                                    </button>
                                </div>

                                <div class="mb-4">
                                    <div class="relative max-w-sm">
                                        <svg class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path>
                                        </svg>
                                        <input type="text" placeholder="학생 검색..." class="pl-8 w-full h-9 px-3 py-1 text-sm bg-background border border-input rounded-md" data-i18n-placeholder="users.searchStudents">
                                    </div>
                                </div>

                                <div class="overflow-x-auto">
                                    <table class="w-full">
                                        <thead>
                                            <tr class="border-b">
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.students">학생</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.enrolledCourses">수강중인 강의</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.completedCourses">완료한 강의</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.status">상태</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.joinDate">가입일</th>
                                                <th class="w-16"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="students-table-body">
                                            <!-- Student rows will be dynamically inserted -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Instructors Tab -->
                            <div id="instructors-content" class="user-tab-content p-6 hidden">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="font-semibold" data-i18n="users.instructors">강사</h3>
                                        <p class="text-sm text-muted-foreground" data-i18n="users.instructorsDescription">강사 계정과 강의 배정을 관리합니다</p>
                                    </div>
                                    <button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">
                                        <span data-i18n="users.addInstructor">강사 추가</span>
                                    </button>
                                </div>

                                <div class="mb-4">
                                    <div class="relative max-w-sm">
                                        <svg class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path>
                                        </svg>
                                        <input type="text" placeholder="강사 검색..." class="pl-8 w-full h-9 px-3 py-1 text-sm bg-background border border-input rounded-md" data-i18n-placeholder="users.searchInstructors">
                                    </div>
                                </div>

                                <div class="overflow-x-auto">
                                    <table class="w-full">
                                        <thead>
                                            <tr class="border-b">
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.instructors">강사</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="courses.allCourses">강의</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.students">학생</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.rating">평점</th>
                                                <th class="text-left py-3 px-4 font-medium" data-i18n="users.expertise">전문 분야</th>
                                                <th class="w-16"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="instructors-table-body">
                                            <!-- Instructor rows will be dynamically inserted -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Courses Page -->
                <div id="page-courses" class="page-content">
                    <div class="space-y-6">
                        <div>
                            <h1 data-i18n="courses.title">강의 관리</h1>
                            <p class="text-muted-foreground" data-i18n="courses.description">플랫폼의 모든 강의를 생성, 편집, 관리합니다.</p>
                        </div>

                        <div class="bg-card border border-border rounded-lg p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div>
                                    <h3 class="font-semibold" data-i18n="courses.allCourses">전체 강의</h3>
                                    <p class="text-sm text-muted-foreground" data-i18n="courses.allCoursesDescription">강의 내용, 가격, 수강 가능 여부를 관리합니다</p>
                                </div>
                                <button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90">
                                    <span data-i18n="courses.createCourse">강의 생성</span>
                                </button>
                            </div>

                            <div class="flex items-center space-x-2 mb-6">
                                <div class="relative flex-1 max-w-sm">
                                    <svg class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path>
                                    </svg>
                                    <input type="text" placeholder="강의 검색..." class="pl-8 w-full h-9 px-3 py-1 text-sm bg-background border border-input rounded-md" data-i18n-placeholder="courses.searchCourses">
                                </div>
                                <button class="px-4 py-2 border border-border rounded-md hover:bg-accent">
                                    <span data-i18n="courses.filter">필터</span>
                                </button>
                            </div>

                            <div id="courses-list" class="space-y-4">
                                <!-- Course cards will be dynamically inserted -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Page -->
                <div id="page-analytics" class="page-content">
                    <!-- Same as dashboard for now -->
                    <div class="space-y-6">
                        <div>
                            <h1 data-i18n="menu.analytics">분석</h1>
                            <p class="text-muted-foreground">플랫폼 분석 및 통계 정보를 확인합니다.</p>
                        </div>
                        <!-- Analytics content would go here -->
                    </div>
                </div>

                <!-- Settings Page -->
                <div id="page-settings" class="page-content">
                    <div class="space-y-6">
                        <div>
                            <h1 data-i18n="settings.title">설정</h1>
                            <p class="text-muted-foreground" data-i18n="settings.description">LMS 플랫폼 설정과 환경설정을 구성합니다.</p>
                        </div>

                        <!-- Settings Tabs -->
                        <div class="bg-card border border-border rounded-lg">
                            <div class="border-b">
                                <nav class="flex space-x-8 px-6">
                                    <button class="settings-tab active py-4 border-b-2 border-primary text-primary font-medium" data-tab="general">
                                        <span data-i18n="settings.general">일반</span>
                                    </button>
                                    <button class="settings-tab py-4 border-b-2 border-transparent hover:text-primary" data-tab="notifications">
                                        <span data-i18n="settings.notifications">알림</span>
                                    </button>
                                    <button class="settings-tab py-4 border-b-2 border-transparent hover:text-primary" data-tab="security">
                                        <span data-i18n="settings.security">보안</span>
                                    </button>
                                    <button class="settings-tab py-4 border-b-2 border-transparent hover:text-primary" data-tab="integrations">
                                        <span data-i18n="settings.integrations">연동</span>
                                    </button>
                                </nav>
                            </div>

                            <!-- Settings content tabs will be added in JavaScript -->
                            <div id="settings-content" class="p-6">
                                <!-- Content will be dynamically loaded -->
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="i18n.js"></script>
    <script src="script.js"></script>
</body>
</html>