// Internationalization (i18n) module
class I18n {
    constructor() {
        this.currentLanguage = 'ko';
        this.translations = {
            ko: {
                // Header
                'header.search': '검색...',
                'header.admin': '관리자',
                'header.profile': '프로필',
                'header.billing': '결제',
                'header.team': '팀',
                'header.subscription': '구독',
                'header.logout': '로그아웃',
                'header.maxTabs': '최대 10개 탭',
                
                // Platform
                'platform.name': '에듀런 LMS',
                'platform.logo': '에',
                
                // Menu
                'menu.dashboard': '대시보드',
                'menu.users': '사용자 관리',
                'menu.courses': '강의 관리',
                'menu.analytics': '분석',
                'menu.settings': '설정',
                
                // Dashboard
                'dashboard.title': '대시보드',
                'dashboard.welcome': 'LMS 관리자 대시보드에 오신 것을 환영합니다. 플랫폼 현황을 한눈에 확인하세요.',
                'dashboard.totalStudents': '전체 학생',
                'dashboard.students': '명',
                'dashboard.activeCourses': '활성 강의',
                'dashboard.courses': '개',
                'dashboard.instructors': '강사',
                'dashboard.revenue': '수익',
                'dashboard.lastMonthIncrease': '지난달 대비 +12%',
                'dashboard.newThisWeek': '이번주 +3개 신규',
                'dashboard.joinedThisMonth': '이번달 +2명 가입',
                'dashboard.lastMonthRevenue': '지난달 대비 +18%',
                'dashboard.enrollmentTrend': '학생 등록 추이',
                'dashboard.enrollmentDescription': '최근 6개월간 월별 학생 등록 현황',
                'dashboard.completionRates': '강의 완료율',
                'dashboard.completionDescription': '각 강의별 학생 완료 비율',
                
                // User Management
                'users.title': '사용자 관리',
                'users.description': '플랫폼의 학생과 강사를 관리합니다.',
                'users.students': '학생',
                'users.instructors': '강사',
                'users.studentsDescription': '학생 계정과 수강 현황을 관리합니다',
                'users.instructorsDescription': '강사 계정과 강의 배정을 관리합니다',
                'users.addStudent': '학생 추가',
                'users.addInstructor': '강사 추가',
                'users.searchStudents': '학생 검색...',
                'users.searchInstructors': '강사 검색...',
                'users.enrolledCourses': '수강중인 강의',
                'users.completedCourses': '완료한 강의',
                'users.status': '상태',
                'users.joinDate': '가입일',
                'users.rating': '평점',
                'users.expertise': '전문 분야',
                'users.active': '활성',
                'users.inactive': '비활성',
                'users.actions': '작업',
                'users.sendMessage': '메시지 보내기',
                'users.viewProfile': '프로필 보기',
                'users.suspendUser': '사용자 정지',
                'users.assignCourse': '강의 배정',
                'users.removeInstructor': '강사 제거',
                
                // Course Management
                'courses.title': '강의 관리',
                'courses.description': '플랫폼의 모든 강의를 생성, 편집, 관리합니다.',
                'courses.allCourses': '전체 강의',
                'courses.allCoursesDescription': '강의 내용, 가격, 수강 가능 여부를 관리합니다',
                'courses.createCourse': '강의 생성',
                'courses.searchCourses': '강의 검색...',
                'courses.filter': '필터',
                'courses.active': '활성',
                'courses.draft': '초안',
                'courses.students': '명 학생',
                'courses.price': '가격',
                'courses.progress': '강의 진행률',
                'courses.editCourse': '강의 편집',
                'courses.viewAnalytics': '분석 보기',
                'courses.duplicate': '복제',
                'courses.publish': '게시',
                'courses.unpublish': '게시 중단',
                'courses.deleteCourse': '강의 삭제',
                
                // Settings
                'settings.title': '설정',
                'settings.description': 'LMS 플랫폼 설정과 환경설정을 구성합니다.',
                'settings.general': '일반',
                'settings.notifications': '알림',
                'settings.security': '보안',
                'settings.integrations': '연동',
                
                // Months
                'month.january': '1월',
                'month.february': '2월',
                'month.march': '3월',
                'month.april': '4월',
                'month.may': '5월',
                'month.june': '6월',
                
                // Course categories
                'category.webDevelopment': '웹 개발',
                'category.datascience': '데이터 사이언스',
                'category.uiux': 'UI/UX',
                'category.mobileDevelopment': '모바일 개발',
                'category.devops': 'DevOps',
                'category.design': '디자인',
            },
            en: {
                // Header
                'header.search': 'Search...',
                'header.admin': 'Admin User',
                'header.profile': 'Profile',
                'header.billing': 'Billing',
                'header.team': 'Team',
                'header.subscription': 'Subscription',
                'header.logout': 'Log out',
                'header.maxTabs': 'Max 10 tabs',
                
                // Platform
                'platform.name': 'EduLearn LMS',
                'platform.logo': 'E',
                
                // Menu
                'menu.dashboard': 'Dashboard',
                'menu.users': 'User Management',
                'menu.courses': 'Course Management',
                'menu.analytics': 'Analytics',
                'menu.settings': 'Settings',
                
                // Dashboard
                'dashboard.title': 'Dashboard',
                'dashboard.welcome': 'Welcome to your LMS admin dashboard. Here\'s an overview of your platform.',
                'dashboard.totalStudents': 'Total Students',
                'dashboard.students': '',
                'dashboard.activeCourses': 'Active Courses',
                'dashboard.courses': '',
                'dashboard.instructors': 'Instructors',
                'dashboard.revenue': 'Revenue',
                'dashboard.lastMonthIncrease': '+12% from last month',
                'dashboard.newThisWeek': '+3 new this week',
                'dashboard.joinedThisMonth': '+2 joined this month',
                'dashboard.lastMonthRevenue': '+18% from last month',
                'dashboard.enrollmentTrend': 'Student Enrollment Trend',
                'dashboard.enrollmentDescription': 'Monthly student registrations over the past 6 months',
                'dashboard.completionRates': 'Course Completion Rates',
                'dashboard.completionDescription': 'Percentage of students completing each course',
                
                // User Management
                'users.title': 'User Management',
                'users.description': 'Manage students and instructors on your platform.',
                'users.students': 'Students',
                'users.instructors': 'Instructors',
                'users.studentsDescription': 'Manage student accounts and enrollments',
                'users.instructorsDescription': 'Manage instructor accounts and course assignments',
                'users.addStudent': 'Add Student',
                'users.addInstructor': 'Add Instructor',
                'users.searchStudents': 'Search students...',
                'users.searchInstructors': 'Search instructors...',
                'users.enrolledCourses': 'Enrolled Courses',
                'users.completedCourses': 'Completed Courses',
                'users.status': 'Status',
                'users.joinDate': 'Join Date',
                'users.rating': 'Rating',
                'users.expertise': 'Expertise',
                'users.active': 'Active',
                'users.inactive': 'Inactive',
                'users.actions': 'Actions',
                'users.sendMessage': 'Send Message',
                'users.viewProfile': 'View Profile',
                'users.suspendUser': 'Suspend User',
                'users.assignCourse': 'Assign Course',
                'users.removeInstructor': 'Remove Instructor',
                
                // Course Management
                'courses.title': 'Course Management',
                'courses.description': 'Create, edit, and manage all courses on your platform.',
                'courses.allCourses': 'All Courses',
                'courses.allCoursesDescription': 'Manage course content, pricing, and availability',
                'courses.createCourse': 'Create Course',
                'courses.searchCourses': 'Search courses...',
                'courses.filter': 'Filter',
                'courses.active': 'Active',
                'courses.draft': 'Draft',
                'courses.students': ' students',
                'courses.price': 'Price',
                'courses.progress': 'Course Progress',
                'courses.editCourse': 'Edit Course',
                'courses.viewAnalytics': 'View Analytics',
                'courses.duplicate': 'Duplicate',
                'courses.publish': 'Publish',
                'courses.unpublish': 'Unpublish',
                'courses.deleteCourse': 'Delete Course',
                
                // Settings
                'settings.title': 'Settings',
                'settings.description': 'Configure your LMS platform settings and preferences.',
                'settings.general': 'General',
                'settings.notifications': 'Notifications',
                'settings.security': 'Security',
                'settings.integrations': 'Integrations',
                
                // Months
                'month.january': 'Jan',
                'month.february': 'Feb',
                'month.march': 'Mar',
                'month.april': 'Apr',
                'month.may': 'May',
                'month.june': 'Jun',
                
                // Course categories
                'category.webDevelopment': 'Web Development',
                'category.datascience': 'Data Science',
                'category.uiux': 'UI/UX',
                'category.mobileDevelopment': 'Mobile Development',
                'category.devops': 'DevOps',
                'category.design': 'Design',
            }
        };
    }

    setLanguage(lang) {
        this.currentLanguage = lang;
        this.updateUI();
    }

    t(key) {
        return this.translations[this.currentLanguage][key] || key;
    }

    updateUI() {
        // Update all elements with data-i18n attribute
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = this.t(key);
        });

        // Update placeholders
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });

        // Update document title
        document.title = this.t('platform.name') + ' - ' + this.t('menu.dashboard');

        // Update HTML lang attribute
        document.documentElement.lang = this.currentLanguage;

        // Update specific dynamic content
        this.updateDynamicContent();
    }

    updateDynamicContent() {
        // Update stats based on language
        if (this.currentLanguage === 'ko') {
            document.getElementById('total-students').textContent = '2,847명';
            document.getElementById('active-courses').textContent = '127개';
            document.getElementById('total-instructors').textContent = '89명';
            document.getElementById('total-revenue').textContent = '₩54,231,000';
        } else {
            document.getElementById('total-students').textContent = '2,847';
            document.getElementById('active-courses').textContent = '127';
            document.getElementById('total-instructors').textContent = '89';
            document.getElementById('total-revenue').textContent = '$54,231';
        }

        // Update charts if they exist
        if (window.enrollmentChart) {
            this.updateCharts();
        }
    }

    updateCharts() {
        const months = [
            this.t('month.january'),
            this.t('month.february'),
            this.t('month.march'),
            this.t('month.april'),
            this.t('month.may'),
            this.t('month.june')
        ];

        const categories = [
            this.t('category.webDevelopment'),
            this.t('category.datascience'),
            this.t('category.uiux'),
            this.t('category.mobileDevelopment'),
            this.t('category.devops')
        ];

        // Update enrollment chart
        if (window.enrollmentChart) {
            window.enrollmentChart.data.labels = months;
            window.enrollmentChart.update();
        }

        // Update completion chart
        if (window.completionChart) {
            window.completionChart.data.labels = categories;
            window.completionChart.update();
        }
    }
}

// Create global i18n instance
window.i18n = new I18n();