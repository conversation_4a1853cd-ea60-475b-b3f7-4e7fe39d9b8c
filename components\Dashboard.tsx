import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "./ui/card";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { Users, BookOpen, GraduationCap, TrendingUp } from 'lucide-react';
import { useLanguage } from './i18n/LanguageProvider';

export function Dashboard() {
  const { t, language } = useLanguage();

  const enrollmentData = [
    { month: t('month.january'), students: 1200 },
    { month: t('month.february'), students: 1400 },
    { month: t('month.march'), students: 1600 },
    { month: t('month.april'), students: 1800 },
    { month: t('month.may'), students: 2100 },
    { month: t('month.june'), students: 2300 },
  ];

  const courseCompletionData = [
    { course: t('category.webDevelopment'), completion: 85 },
    { course: t('category.datascience'), completion: 78 },
    { course: t('category.uiux'), completion: 92 },
    { course: t('category.mobileDevelopment'), completion: 73 },
    { course: t('category.devops'), completion: 81 },
  ];

  const formatRevenue = (amount: number) => {
    if (language === 'ko') {
      return `₩${amount.toLocaleString('ko-KR')}`;
    }
    return `$${amount.toLocaleString('en-US')}`;
  };

  const formatStudentCount = (count: number) => {
    if (language === 'ko') {
      return `${count.toLocaleString('ko-KR')}${t('dashboard.students')}`;
    }
    return count.toLocaleString('en-US');
  };

  const formatCourseCount = (count: number) => {
    if (language === 'ko') {
      return `${count}${t('dashboard.courses')}`;
    }
    return count.toString();
  };

  return (
    <div className="space-y-6">
      <div>
        <h1>{t('dashboard.title')}</h1>
        <p className="text-muted-foreground">
          {t('dashboard.welcome')}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm">{t('dashboard.totalStudents')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl">{formatStudentCount(2847)}</div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.lastMonthIncrease')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm">{t('dashboard.activeCourses')}</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl">{formatCourseCount(127)}</div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.newThisWeek')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm">{t('dashboard.instructors')}</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl">{language === 'ko' ? '89명' : '89'}</div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.joinedThisMonth')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm">{t('dashboard.revenue')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl">{formatRevenue(language === 'ko' ? 54231000 : 54231)}</div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.lastMonthRevenue')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t('dashboard.enrollmentTrend')}</CardTitle>
            <CardDescription>{t('dashboard.enrollmentDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={enrollmentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="students" stroke="hsl(var(--primary))" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('dashboard.completionRates')}</CardTitle>
            <CardDescription>{t('dashboard.completionDescription')}</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={courseCompletionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="course" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="completion" fill="hsl(var(--primary))" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}