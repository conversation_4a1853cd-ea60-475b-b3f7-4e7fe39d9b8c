import { createContext, useContext, useState, ReactNode } from 'react';

type Language = 'ko' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>('ko');

  const translations = {
    ko: {
      // Header
      'header.search': '검색...',
      'header.admin': '관리자',
      'header.profile': '프로필',
      'header.billing': '결제',
      'header.team': '팀',
      'header.subscription': '구독',
      'header.logout': '로그아웃',
      'header.maxTabs': '최대 10개 탭',
      
      // Platform
      'platform.name': '에듀런 LMS',
      'platform.logo': '에',
      
      // Menu
      'menu.dashboard': '대시보드',
      'menu.users': '사용자 관리',
      'menu.courses': '강의 관리',
      'menu.analytics': '분석',
      'menu.settings': '설정',
      
      // Dashboard
      'dashboard.title': '대시보드',
      'dashboard.welcome': 'LMS 관리자 대시보드에 오신 것을 환영합니다. 플랫폼 현황을 한눈에 확인하세요.',
      'dashboard.totalStudents': '전체 학생',
      'dashboard.students': '명',
      'dashboard.activeCourses': '활성 강의',
      'dashboard.courses': '개',
      'dashboard.instructors': '강사',
      'dashboard.revenue': '수익',
      'dashboard.lastMonthIncrease': '지난달 대비 +12%',
      'dashboard.newThisWeek': '이번주 +3개 신규',
      'dashboard.joinedThisMonth': '이번달 +2명 가입',
      'dashboard.lastMonthRevenue': '지난달 대비 +18%',
      'dashboard.enrollmentTrend': '학생 등록 추이',
      'dashboard.enrollmentDescription': '최근 6개월간 월별 학생 등록 현황',
      'dashboard.completionRates': '강의 완료율',
      'dashboard.completionDescription': '각 강의별 학생 완료 비율',
      
      // User Management
      'users.title': '사용자 관리',
      'users.description': '플랫폼의 학생과 강사를 관리합니다.',
      'users.students': '학생',
      'users.instructors': '강사',
      'users.studentsDescription': '학생 계정과 수강 현황을 관리합니다',
      'users.instructorsDescription': '강사 계정과 강의 배정을 관리합니다',
      'users.addStudent': '학생 추가',
      'users.addInstructor': '강사 추가',
      'users.searchStudents': '학생 검색...',
      'users.searchInstructors': '강사 검색...',
      'users.enrolledCourses': '수강중인 강의',
      'users.completedCourses': '완료한 강의',
      'users.status': '상태',
      'users.joinDate': '가입일',
      'users.rating': '평점',
      'users.expertise': '전문 분야',
      'users.active': '활성',
      'users.inactive': '비활성',
      'users.actions': '작업',
      'users.sendMessage': '메시지 보내기',
      'users.viewProfile': '프로필 보기',
      'users.suspendUser': '사용자 정지',
      'users.assignCourse': '강의 배정',
      'users.removeInstructor': '강사 제거',
      
      // Course Management
      'courses.title': '강의 관리',
      'courses.description': '플랫폼의 모든 강의를 생성, 편집, 관리합니다.',
      'courses.allCourses': '전체 강의',
      'courses.allCoursesDescription': '강의 내용, 가격, 수강 가능 여부를 관리합니다',
      'courses.createCourse': '강의 생성',
      'courses.searchCourses': '강의 검색...',
      'courses.filter': '필터',
      'courses.active': '활성',
      'courses.draft': '초안',
      'courses.students': '명 학생',
      'courses.price': '가격',
      'courses.progress': '강의 진행률',
      'courses.editCourse': '강의 편집',
      'courses.viewAnalytics': '분석 보기',
      'courses.duplicate': '복제',
      'courses.publish': '게시',
      'courses.unpublish': '게시 중단',
      'courses.deleteCourse': '강의 삭제',
      
      // Settings
      'settings.title': '설정',
      'settings.description': 'LMS 플랫폼 설정과 환경설정을 구성합니다.',
      'settings.general': '일반',
      'settings.notifications': '알림',
      'settings.security': '보안',
      'settings.integrations': '연동',
      'settings.platformInfo': '플랫폼 정보',
      'settings.platformInfoDescription': 'LMS 플랫폼의 기본 정보입니다',
      'settings.platformName': '플랫폼 이름',
      'settings.platformDescription': '플랫폼 설명',
      'settings.platformLogo': '플랫폼 로고',
      'settings.changeLogo': '로고 변경',
      'settings.contactEmail': '연락처 이메일',
      'settings.supportPhone': '고객지원 전화',
      'settings.timezone': '기본 시간대',
      'settings.saveChanges': '변경사항 저장',
      'settings.emailNotifications': '이메일 알림',
      'settings.emailNotificationsDescription': '이메일 알림 설정을 구성합니다',
      'settings.newStudentRegistration': '신규 학생 등록',
      'settings.newStudentDescription': '새로운 학생이 등록할 때 이메일을 보냅니다',
      'settings.courseCompletion': '강의 완료',
      'settings.courseCompletionDescription': '학생이 강의를 완료할 때 이메일을 보냅니다',
      'settings.paymentReceived': '결제 수신',
      'settings.paymentDescription': '결제가 완료될 때 이메일을 보냅니다',
      'settings.systemMaintenance': '시스템 점검',
      'settings.maintenanceDescription': '예정된 점검 전에 이메일을 보냅니다',
      'settings.notificationEmail': '알림 이메일',
      'settings.savePreferences': '환경설정 저장',
      'settings.securitySettings': '보안 설정',
      'settings.securityDescription': '보안 및 인증 설정을 관리합니다',
      'settings.twoFactorAuth': '2단계 인증',
      'settings.twoFactorDescription': '관리자 계정에 2FA를 요구합니다',
      'settings.passwordRequirements': '비밀번호 요구사항',
      'settings.passwordDescription': '강력한 비밀번호 정책을 적용합니다',
      'settings.sessionTimeout': '세션 만료 시간 (분)',
      'settings.loginAttemptLimit': '로그인 시도 제한',
      'settings.loginAttemptDescription': '로그인 실패 시 계정을 잠급니다',
      'settings.updateSecurity': '보안 설정 업데이트',
      'settings.thirdPartyIntegrations': '외부 서비스 연동',
      'settings.integrationsDescription': '외부 서비스 및 도구와 연결합니다',
      'settings.emailProvider': '이메일 제공업체',
      'settings.emailProviderDescription': '이메일 발송을 위한 SendGrid 연동',
      'settings.paymentGateway': '결제 게이트웨이',
      'settings.paymentGatewayDescription': '결제 처리를 위한 Stripe 연동',
      'settings.analytics': '분석',
      'settings.analyticsDescription': 'Google Analytics 연동',
      'settings.videoHosting': '동영상 호스팅',
      'settings.videoHostingDescription': '동영상 콘텐츠를 위한 Vimeo 연동',
      'settings.connected': '연결됨',
      'settings.notConnected': '연결되지 않음',
      'settings.configure': '설정',
      'settings.connect': '연결',
      
      // Months
      'month.january': '1월',
      'month.february': '2월',
      'month.march': '3월',
      'month.april': '4월',
      'month.may': '5월',
      'month.june': '6월',
      
      // Course categories
      'category.webDevelopment': '웹 개발',
      'category.datascience': '데이터 사이언스',
      'category.uiux': 'UI/UX',
      'category.mobileDevelopment': '모바일 개발',
      'category.devops': 'DevOps',
      'category.design': '디자인',
    },
    en: {
      // Header
      'header.search': 'Search...',
      'header.admin': 'Admin User',
      'header.profile': 'Profile',
      'header.billing': 'Billing',
      'header.team': 'Team',
      'header.subscription': 'Subscription',
      'header.logout': 'Log out',
      'header.maxTabs': 'Max 10 tabs',
      
      // Platform
      'platform.name': 'EduLearn LMS',
      'platform.logo': 'E',
      
      // Menu
      'menu.dashboard': 'Dashboard',
      'menu.users': 'User Management',
      'menu.courses': 'Course Management',
      'menu.analytics': 'Analytics',
      'menu.settings': 'Settings',
      
      // Dashboard
      'dashboard.title': 'Dashboard',
      'dashboard.welcome': 'Welcome to your LMS admin dashboard. Here\'s an overview of your platform.',
      'dashboard.totalStudents': 'Total Students',
      'dashboard.students': '',
      'dashboard.activeCourses': 'Active Courses',
      'dashboard.courses': '',
      'dashboard.instructors': 'Instructors',
      'dashboard.revenue': 'Revenue',
      'dashboard.lastMonthIncrease': '+12% from last month',
      'dashboard.newThisWeek': '+3 new this week',
      'dashboard.joinedThisMonth': '+2 joined this month',
      'dashboard.lastMonthRevenue': '+18% from last month',
      'dashboard.enrollmentTrend': 'Student Enrollment Trend',
      'dashboard.enrollmentDescription': 'Monthly student registrations over the past 6 months',
      'dashboard.completionRates': 'Course Completion Rates',
      'dashboard.completionDescription': 'Percentage of students completing each course',
      
      // User Management
      'users.title': 'User Management',
      'users.description': 'Manage students and instructors on your platform.',
      'users.students': 'Students',
      'users.instructors': 'Instructors',
      'users.studentsDescription': 'Manage student accounts and enrollments',
      'users.instructorsDescription': 'Manage instructor accounts and course assignments',
      'users.addStudent': 'Add Student',
      'users.addInstructor': 'Add Instructor',
      'users.searchStudents': 'Search students...',
      'users.searchInstructors': 'Search instructors...',
      'users.enrolledCourses': 'Enrolled Courses',
      'users.completedCourses': 'Completed Courses',
      'users.status': 'Status',
      'users.joinDate': 'Join Date',
      'users.rating': 'Rating',
      'users.expertise': 'Expertise',
      'users.active': 'Active',
      'users.inactive': 'Inactive',
      'users.actions': 'Actions',
      'users.sendMessage': 'Send Message',
      'users.viewProfile': 'View Profile',
      'users.suspendUser': 'Suspend User',
      'users.assignCourse': 'Assign Course',
      'users.removeInstructor': 'Remove Instructor',
      
      // Course Management
      'courses.title': 'Course Management',
      'courses.description': 'Create, edit, and manage all courses on your platform.',
      'courses.allCourses': 'All Courses',
      'courses.allCoursesDescription': 'Manage course content, pricing, and availability',
      'courses.createCourse': 'Create Course',
      'courses.searchCourses': 'Search courses...',
      'courses.filter': 'Filter',
      'courses.active': 'Active',
      'courses.draft': 'Draft',
      'courses.students': ' students',
      'courses.price': 'Price',
      'courses.progress': 'Course Progress',
      'courses.editCourse': 'Edit Course',
      'courses.viewAnalytics': 'View Analytics',
      'courses.duplicate': 'Duplicate',
      'courses.publish': 'Publish',
      'courses.unpublish': 'Unpublish',
      'courses.deleteCourse': 'Delete Course',
      
      // Settings
      'settings.title': 'Settings',
      'settings.description': 'Configure your LMS platform settings and preferences.',
      'settings.general': 'General',
      'settings.notifications': 'Notifications',
      'settings.security': 'Security',
      'settings.integrations': 'Integrations',
      'settings.platformInfo': 'Platform Information',
      'settings.platformInfoDescription': 'Basic information about your LMS platform',
      'settings.platformName': 'Platform Name',
      'settings.platformDescription': 'Platform Description',
      'settings.platformLogo': 'Platform Logo',
      'settings.changeLogo': 'Change Logo',
      'settings.contactEmail': 'Contact Email',
      'settings.supportPhone': 'Support Phone',
      'settings.timezone': 'Default Timezone',
      'settings.saveChanges': 'Save Changes',
      'settings.emailNotifications': 'Email Notifications',
      'settings.emailNotificationsDescription': 'Configure email notification preferences',
      'settings.newStudentRegistration': 'New Student Registration',
      'settings.newStudentDescription': 'Send email when a new student registers',
      'settings.courseCompletion': 'Course Completion',
      'settings.courseCompletionDescription': 'Send email when a student completes a course',
      'settings.paymentReceived': 'Payment Received',
      'settings.paymentDescription': 'Send email when payment is received',
      'settings.systemMaintenance': 'System Maintenance',
      'settings.maintenanceDescription': 'Send email before scheduled maintenance',
      'settings.notificationEmail': 'Notification Email',
      'settings.savePreferences': 'Save Preferences',
      'settings.securitySettings': 'Security Settings',
      'settings.securityDescription': 'Manage security and authentication settings',
      'settings.twoFactorAuth': 'Two-Factor Authentication',
      'settings.twoFactorDescription': 'Require 2FA for admin accounts',
      'settings.passwordRequirements': 'Password Requirements',
      'settings.passwordDescription': 'Enforce strong password policy',
      'settings.sessionTimeout': 'Session Timeout (minutes)',
      'settings.loginAttemptLimit': 'Login Attempt Limit',
      'settings.loginAttemptDescription': 'Lock account after failed login attempts',
      'settings.updateSecurity': 'Update Security Settings',
      'settings.thirdPartyIntegrations': 'Third-Party Integrations',
      'settings.integrationsDescription': 'Connect with external services and tools',
      'settings.emailProvider': 'Email Provider',
      'settings.emailProviderDescription': 'SendGrid integration for email delivery',
      'settings.paymentGateway': 'Payment Gateway',
      'settings.paymentGatewayDescription': 'Stripe integration for payments',
      'settings.analytics': 'Analytics',
      'settings.analyticsDescription': 'Google Analytics integration',
      'settings.videoHosting': 'Video Hosting',
      'settings.videoHostingDescription': 'Vimeo integration for video content',
      'settings.connected': 'Connected',
      'settings.notConnected': 'Not Connected',
      'settings.configure': 'Configure',
      'settings.connect': 'Connect',
      
      // Months
      'month.january': 'Jan',
      'month.february': 'Feb',
      'month.march': 'Mar',
      'month.april': 'Apr',
      'month.may': 'May',
      'month.june': 'Jun',
      
      // Course categories
      'category.webDevelopment': 'Web Development',
      'category.datascience': 'Data Science',
      'category.uiux': 'UI/UX',
      'category.mobileDevelopment': 'Mobile Development',
      'category.devops': 'DevOps',
      'category.design': 'Design',
    }
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}