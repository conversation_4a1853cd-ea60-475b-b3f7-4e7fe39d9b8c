import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "./ui/dropdown-menu";
import { Progress } from "./ui/progress";
import { Search, Plus, MoreHorizontal, Users, Clock, Star } from 'lucide-react';
import { useLanguage } from './i18n/LanguageProvider';

export function CourseManagement() {
  const { t, language } = useLanguage();

  const courses = [
    {
      id: 1,
      title: language === 'ko' ? "완전한 웹 개발 부트캠프" : "Complete Web Development Bootcamp",
      instructor: language === 'ko' ? "김영훈 교수" : "Dr. <PERSON>",
      students: 145,
      duration: language === 'ko' ? "12주" : "12 weeks",
      progress: 85,
      rating: 4.8,
      status: "active",
      category: t('category.webDevelopment'),
      price: language === 'ko' ? "₩299,000" : "$299",
      thumbnail: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=200&fit=crop"
    },
    {
      id: 2,
      title: language === 'ko' ? "파이썬으로 배우는 데이터 사이언스" : "Data Science with Python",
      instructor: language === 'ko' ? "이수진 박사" : "Prof. Michael Chen",
      students: 89,
      duration: language === 'ko' ? "10주" : "10 weeks",
      progress: 78,
      rating: 4.9,
      status: "active",
      category: t('category.datascience'),
      price: language === 'ko' ? "₩399,000" : "$399",
      thumbnail: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=200&fit=crop"
    },
    {
      id: 3,
      title: language === 'ko' ? "UI/UX 디자인 기초" : "UI/UX Design Fundamentals",
      instructor: language === 'ko' ? "박현우 교수" : "Dr. Emma Thompson",
      students: 203,
      duration: language === 'ko' ? "8주" : "8 weeks",
      progress: 92,
      rating: 4.7,
      status: "active",
      category: t('category.design'),
      price: language === 'ko' ? "₩249,000" : "$249",
      thumbnail: "https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?w=400&h=200&fit=crop"
    },
    {
      id: 4,
      title: language === 'ko' ? "모바일 앱 개발" : "Mobile App Development",
      instructor: language === 'ko' ? "김영훈 교수" : "Dr. Sarah Miller",
      students: 76,
      duration: language === 'ko' ? "14주" : "14 weeks",
      progress: 45,
      rating: 4.6,
      status: "draft",
      category: t('category.mobileDevelopment'),
      price: language === 'ko' ? "₩349,000" : "$349",
      thumbnail: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=200&fit=crop"
    },
    {
      id: 5,
      title: language === 'ko' ? "DevOps와 클라우드 컴퓨팅" : "DevOps and Cloud Computing",
      instructor: language === 'ko' ? "이수진 박사" : "Prof. Michael Chen",
      students: 112,
      duration: language === 'ko' ? "16주" : "16 weeks",
      progress: 91,
      rating: 4.8,
      status: "active",
      category: t('category.devops'),
      price: language === 'ko' ? "₩449,000" : "$449",
      thumbnail: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=200&fit=crop"
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1>{t('courses.title')}</h1>
        <p className="text-muted-foreground">
          {t('courses.description')}
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t('courses.allCourses')}</CardTitle>
              <CardDescription>
                {t('courses.allCoursesDescription')}
              </CardDescription>
            </div>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              {t('courses.createCourse')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder={t('courses.searchCourses')} className="pl-8" />
            </div>
            <Button variant="outline">{t('courses.filter')}</Button>
          </div>

          <div className="space-y-4">
            {courses.map((course) => (
              <Card key={course.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex">
                    <div className="w-48 h-32 bg-muted">
                      <img 
                        src={course.thumbnail} 
                        alt={course.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 p-6">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold">{course.title}</h3>
                            <Badge variant={course.status === 'active' ? 'default' : 'secondary'}>
                              {course.status === 'active' ? t('courses.active') : t('courses.draft')}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <div className="flex items-center">
                              <Avatar className="h-5 w-5 mr-2">
                                <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${course.instructor}`} />
                                <AvatarFallback>{course.instructor.substring(0, 2)}</AvatarFallback>
                              </Avatar>
                              {course.instructor}
                            </div>
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-1" />
                              {course.students}{t('courses.students')}
                            </div>
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {course.duration}
                            </div>
                            <div className="flex items-center">
                              <Star className="h-4 w-4 mr-1" />
                              {course.rating}
                            </div>
                          </div>
                          <Badge variant="outline">{course.category}</Badge>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <p className="font-semibold">{course.price}</p>
                            <p className="text-sm text-muted-foreground">{t('courses.price')}</p>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>{t('users.actions')}</DropdownMenuLabel>
                              <DropdownMenuItem>{t('courses.editCourse')}</DropdownMenuItem>
                              <DropdownMenuItem>{t('courses.viewAnalytics')}</DropdownMenuItem>
                              <DropdownMenuItem>{t('courses.duplicate')}</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                {course.status === 'active' ? t('courses.unpublish') : t('courses.publish')}
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-destructive">
                                {t('courses.deleteCourse')}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span>{t('courses.progress')}</span>
                          <span>{course.progress}%</span>
                        </div>
                        <Progress value={course.progress} className="h-2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}