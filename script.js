// LMS Admin Panel JavaScript
class LMSAdmin {
    constructor() {
        this.tabs = [
            { id: 'dashboard', labelKey: 'menu.dashboard', icon: 'dashboard' }
        ];
        this.activeTab = 'dashboard';
        this.activePage = 'dashboard';
        this.maxTabs = 10;
        this.isSidebarOpen = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.initializeCharts();
        this.renderTabs();
        this.showPage('dashboard');
        
        // Initialize i18n
        window.i18n.updateUI();
    }

    setupEventListeners() {
        // Sidebar menu items
        document.querySelectorAll('.sidebar-menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const page = e.currentTarget.getAttribute('data-page');
                this.handleMenuClick(page);
            });
        });

        // Language selector
        const langSelector = document.getElementById('language-selector');
        const langMenu = document.getElementById('language-menu');
        
        langSelector.addEventListener('click', (e) => {
            e.stopPropagation();
            langMenu.classList.toggle('hidden');
        });

        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const lang = e.currentTarget.getAttribute('data-lang');
                window.i18n.setLanguage(lang);
                langMenu.classList.add('hidden');
            });
        });

        // User menu
        const userMenuTrigger = document.getElementById('user-menu-trigger');
        const userMenu = document.getElementById('user-menu');
        
        userMenuTrigger.addEventListener('click', (e) => {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', () => {
            langMenu.classList.add('hidden');
            userMenu.classList.add('hidden');
        });

        // Sidebar toggle for mobile
        const sidebarToggle = document.getElementById('sidebar-toggle');
        sidebarToggle?.addEventListener('click', () => {
            this.toggleSidebar();
        });

        // User tabs
        this.setupUserTabs();
        
        // Settings tabs
        this.setupSettingsTabs();

        // Window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                this.closeSidebar();
            }
        });
    }

    setupUserTabs() {
        document.querySelectorAll('.user-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.getAttribute('data-tab');
                this.switchUserTab(tabName);
            });
        });
    }

    setupSettingsTabs() {
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.currentTarget.getAttribute('data-tab');
                this.switchSettingsTab(tabName);
            });
        });
    }

    handleMenuClick(pageId) {
        const menuItems = {
            'dashboard': { id: 'dashboard', labelKey: 'menu.dashboard', icon: 'dashboard' },
            'users': { id: 'users', labelKey: 'menu.users', icon: 'users' },
            'courses': { id: 'courses', labelKey: 'menu.courses', icon: 'courses' },
            'analytics': { id: 'analytics', labelKey: 'menu.analytics', icon: 'analytics' },
            'settings': { id: 'settings', labelKey: 'menu.settings', icon: 'settings' }
        };

        const menuItem = menuItems[pageId];
        if (!menuItem) return;

        const existingTab = this.tabs.find(tab => tab.id === menuItem.id);
        
        if (existingTab) {
            // Switch to existing tab
            this.activeTab = menuItem.id;
        } else {
            // Add new tab
            if (this.tabs.length < this.maxTabs) {
                this.tabs.push(menuItem);
                this.activeTab = menuItem.id;
            } else {
                // Remove oldest tab and add new one
                this.tabs.shift();
                this.tabs.push(menuItem);
                this.activeTab = menuItem.id;
            }
        }

        this.renderTabs();
        this.showPage(this.activeTab);
        this.updateActiveMenuItem(pageId);
    }

    renderTabs() {
        const container = document.getElementById('tab-container');
        container.innerHTML = '';

        this.tabs.forEach(tab => {
            const tabElement = document.createElement('div');
            tabElement.className = `tab group ${this.activeTab === tab.id ? 'active' : ''}`;
            tabElement.innerHTML = `
                ${this.getIconSVG(tab.icon)}
                <span class="text-sm truncate max-w-32">${window.i18n.t(tab.labelKey)}</span>
                ${this.tabs.length > 1 ? `
                    <button class="tab-close" data-tab-id="${tab.id}">
                        <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                ` : ''}
            `;

            // Tab click event
            tabElement.addEventListener('click', (e) => {
                if (!e.target.closest('.tab-close')) {
                    this.activeTab = tab.id;
                    this.renderTabs();
                    this.showPage(tab.id);
                    this.updateActiveMenuItem(tab.id);
                }
            });

            // Close button event
            const closeBtn = tabElement.querySelector('.tab-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.closeTab(tab.id);
                });
            }

            container.appendChild(tabElement);
        });

        // Show/hide max tabs indicator
        const indicator = document.getElementById('max-tabs-indicator');
        if (this.tabs.length >= this.maxTabs) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }

    closeTab(tabId) {
        if (this.tabs.length === 1) return; // Don't close last tab

        const tabIndex = this.tabs.findIndex(tab => tab.id === tabId);
        this.tabs = this.tabs.filter(tab => tab.id !== tabId);

        // If closed tab was active, switch to another tab
        if (this.activeTab === tabId) {
            if (tabIndex === 0) {
                this.activeTab = this.tabs[0].id;
            } else {
                this.activeTab = this.tabs[tabIndex - 1].id;
            }
        }

        this.renderTabs();
        this.showPage(this.activeTab);
        this.updateActiveMenuItem(this.activeTab);
    }

    showPage(pageId) {
        // Hide all pages
        document.querySelectorAll('.page-content').forEach(page => {
            page.classList.remove('active');
        });

        // Show selected page
        const targetPage = document.getElementById(`page-${pageId}`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        this.activePage = pageId;
    }

    updateActiveMenuItem(pageId) {
        document.querySelectorAll('.sidebar-menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeItem = document.querySelector(`[data-page="${pageId}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    switchUserTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.user-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.user-tab-content').forEach(content => {
            content.classList.remove('active');
            content.classList.add('hidden');
        });
        
        const targetContent = document.getElementById(`${tabName}-content`);
        if (targetContent) {
            targetContent.classList.add('active');
            targetContent.classList.remove('hidden');
        }
    }

    switchSettingsTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.settings-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update settings content
        this.loadSettingsContent(tabName);
    }

    loadSettingsContent(tabName) {
        const content = document.getElementById('settings-content');
        
        const settingsContent = {
            general: `
                <div class="space-y-6">
                    <h3 class="font-semibold" data-i18n="settings.platformInfo">플랫폼 정보</h3>
                    <div class="space-y-4">
                        <div class="space-y-2">
                            <label class="font-medium" data-i18n="settings.platformName">플랫폼 이름</label>
                            <input type="text" value="에듀런 LMS" class="w-full h-9 px-3 py-1 text-sm bg-background border border-input rounded-md">
                        </div>
                        <button class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90" data-i18n="settings.saveChanges">변경사항 저장</button>
                    </div>
                </div>
            `,
            notifications: `
                <div class="space-y-6">
                    <h3 class="font-semibold" data-i18n="settings.emailNotifications">이메일 알림</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="font-medium" data-i18n="settings.newStudentRegistration">신규 학생 등록</label>
                                <p class="text-sm text-muted-foreground" data-i18n="settings.newStudentDescription">새로운 학생이 등록할 때 이메일을 보냅니다</p>
                            </div>
                            <input type="checkbox" checked class="w-4 h-4">
                        </div>
                    </div>
                </div>
            `,
            security: `
                <div class="space-y-6">
                    <h3 class="font-semibold" data-i18n="settings.securitySettings">보안 설정</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="font-medium" data-i18n="settings.twoFactorAuth">2단계 인증</label>
                                <p class="text-sm text-muted-foreground" data-i18n="settings.twoFactorDescription">관리자 계정에 2FA를 요구합니다</p>
                            </div>
                            <input type="checkbox" checked class="w-4 h-4">
                        </div>
                    </div>
                </div>
            `,
            integrations: `
                <div class="space-y-6">
                    <h3 class="font-semibold" data-i18n="settings.thirdPartyIntegrations">외부 서비스 연동</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 border rounded-lg">
                            <div class="flex items-center space-x-4">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">📧</div>
                                <div>
                                    <h4 data-i18n="settings.emailProvider">이메일 제공업체</h4>
                                    <p class="text-sm text-muted-foreground" data-i18n="settings.emailProviderDescription">이메일 발송을 위한 SendGrid 연동</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="badge badge-default" data-i18n="settings.connected">연결됨</span>
                                <button class="px-3 py-1 border border-border rounded-md hover:bg-accent" data-i18n="settings.configure">설정</button>
                            </div>
                        </div>
                    </div>
                </div>
            `
        };

        content.innerHTML = settingsContent[tabName] || '';
        
        // Re-apply i18n to new content
        setTimeout(() => {
            window.i18n.updateUI();
        }, 0);
    }

    loadInitialData() {
        // Load students data
        this.loadStudentsData();
        
        // Load instructors data
        this.loadInstructorsData();
        
        // Load courses data
        this.loadCoursesData();
    }

    loadStudentsData() {
        const studentsData = [
            {
                name: window.i18n.currentLanguage === 'ko' ? '김수영' : 'Alice Johnson',
                email: window.i18n.currentLanguage === 'ko' ? '<EMAIL>' : '<EMAIL>',
                enrolledCourses: 3,
                completedCourses: 1,
                status: 'active',
                joinDate: '2024-01-15'
            },
            {
                name: window.i18n.currentLanguage === 'ko' ? '이민호' : 'Bob Smith',
                email: window.i18n.currentLanguage === 'ko' ? '<EMAIL>' : '<EMAIL>',
                enrolledCourses: 2,
                completedCourses: 2,
                status: 'active',
                joinDate: '2024-02-20'
            }
        ];

        const tbody = document.getElementById('students-table-body');
        tbody.innerHTML = studentsData.map(student => `
            <tr class="hover:bg-muted/50">
                <td class="py-3 px-4">
                    <div class="flex items-center space-x-3">
                        <div class="avatar">
                            <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=${student.name}" alt="${student.name}">
                        </div>
                        <div>
                            <p class="font-medium">${student.name}</p>
                            <p class="text-sm text-muted-foreground">${student.email}</p>
                        </div>
                    </div>
                </td>
                <td class="py-3 px-4">${student.enrolledCourses}${window.i18n.currentLanguage === 'ko' ? '개' : ''}</td>
                <td class="py-3 px-4">${student.completedCourses}${window.i18n.currentLanguage === 'ko' ? '개' : ''}</td>
                <td class="py-3 px-4">
                    <span class="badge badge-${student.status === 'active' ? 'default' : 'secondary'}">
                        ${window.i18n.t(student.status === 'active' ? 'users.active' : 'users.inactive')}
                    </span>
                </td>
                <td class="py-3 px-4">${student.joinDate}</td>
                <td class="py-3 px-4">
                    <button class="p-2 hover:bg-accent rounded-md">⋯</button>
                </td>
            </tr>
        `).join('');
    }

    loadInstructorsData() {
        const instructorsData = [
            {
                name: window.i18n.currentLanguage === 'ko' ? '김영훈 교수' : 'Dr. Sarah Miller',
                email: window.i18n.currentLanguage === 'ko' ? '<EMAIL>' : '<EMAIL>',
                courses: 3,
                students: 145,
                rating: 4.8,
                expertise: window.i18n.t('category.webDevelopment')
            }
        ];

        const tbody = document.getElementById('instructors-table-body');
        tbody.innerHTML = instructorsData.map(instructor => `
            <tr class="hover:bg-muted/50">
                <td class="py-3 px-4">
                    <div class="flex items-center space-x-3">
                        <div class="avatar">
                            <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=${instructor.name}" alt="${instructor.name}">
                        </div>
                        <div>
                            <p class="font-medium">${instructor.name}</p>
                            <p class="text-sm text-muted-foreground">${instructor.email}</p>
                        </div>
                    </div>
                </td>
                <td class="py-3 px-4">${instructor.courses}${window.i18n.currentLanguage === 'ko' ? '개' : ''}</td>
                <td class="py-3 px-4">${instructor.students}${window.i18n.currentLanguage === 'ko' ? '명' : ''}</td>
                <td class="py-3 px-4">⭐ ${instructor.rating}</td>
                <td class="py-3 px-4">
                    <span class="badge badge-outline">${instructor.expertise}</span>
                </td>
                <td class="py-3 px-4">
                    <button class="p-2 hover:bg-accent rounded-md">⋯</button>
                </td>
            </tr>
        `).join('');
    }

    loadCoursesData() {
        const coursesData = [
            {
                id: 1,
                title: window.i18n.currentLanguage === 'ko' ? '완전한 웹 개발 부트캠프' : 'Complete Web Development Bootcamp',
                instructor: window.i18n.currentLanguage === 'ko' ? '김영훈 교수' : 'Dr. Sarah Miller',
                students: 145,
                duration: window.i18n.currentLanguage === 'ko' ? '12주' : '12 weeks',
                progress: 85,
                rating: 4.8,
                status: 'active',
                category: window.i18n.t('category.webDevelopment'),
                price: window.i18n.currentLanguage === 'ko' ? '₩299,000' : '$299',
                thumbnail: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=200&fit=crop'
            }
        ];

        const container = document.getElementById('courses-list');
        container.innerHTML = coursesData.map(course => `
            <div class="course-card">
                <div class="flex">
                    <div class="course-thumbnail">
                        <img src="${course.thumbnail}" alt="${course.title}">
                    </div>
                    <div class="flex-1 p-6">
                        <div class="flex items-start justify-between">
                            <div class="space-y-2">
                                <div class="flex items-center space-x-2">
                                    <h3 class="font-semibold">${course.title}</h3>
                                    <span class="badge badge-${course.status === 'active' ? 'default' : 'secondary'}">
                                        ${window.i18n.t(course.status === 'active' ? 'courses.active' : 'courses.draft')}
                                    </span>
                                </div>
                                <div class="flex items-center space-x-4 text-sm text-muted-foreground">
                                    <div class="flex items-center">
                                        <div class="avatar mr-2" style="width: 20px; height: 20px;">
                                            <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=${course.instructor}" alt="${course.instructor}">
                                        </div>
                                        ${course.instructor}
                                    </div>
                                    <div>${course.students}${window.i18n.t('courses.students')}</div>
                                    <div>${course.duration}</div>
                                    <div>⭐ ${course.rating}</div>
                                </div>
                                <span class="badge badge-outline">${course.category}</span>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="text-right">
                                    <p class="font-semibold">${course.price}</p>
                                    <p class="text-sm text-muted-foreground">${window.i18n.t('courses.price')}</p>
                                </div>
                                <button class="p-2 hover:bg-accent rounded-md">⋯</button>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between text-sm mb-2">
                                <span>${window.i18n.t('courses.progress')}</span>
                                <span>${course.progress}%</span>
                            </div>
                            <div class="progress h-2">
                                <div class="progress-bar" style="width: ${course.progress}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    initializeCharts() {
        // Enrollment trend chart
        const enrollmentCtx = document.getElementById('enrollmentChart');
        if (enrollmentCtx) {
            window.enrollmentChart = new Chart(enrollmentCtx, {
                type: 'line',
                data: {
                    labels: [
                        window.i18n.t('month.january'),
                        window.i18n.t('month.february'),
                        window.i18n.t('month.march'),
                        window.i18n.t('month.april'),
                        window.i18n.t('month.may'),
                        window.i18n.t('month.june')
                    ],
                    datasets: [{
                        label: window.i18n.t('dashboard.totalStudents'),
                        data: [1200, 1400, 1600, 1800, 2100, 2300],
                        borderColor: '#030213',
                        backgroundColor: 'rgba(3, 2, 19, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }

        // Completion rates chart
        const completionCtx = document.getElementById('completionChart');
        if (completionCtx) {
            window.completionChart = new Chart(completionCtx, {
                type: 'bar',
                data: {
                    labels: [
                        window.i18n.t('category.webDevelopment'),
                        window.i18n.t('category.datascience'),
                        window.i18n.t('category.uiux'),
                        window.i18n.t('category.mobileDevelopment'),
                        window.i18n.t('category.devops')
                    ],
                    datasets: [{
                        label: window.i18n.t('dashboard.completionRates'),
                        data: [85, 78, 92, 73, 81],
                        backgroundColor: '#030213',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
    }

    getIconSVG(iconName) {
        const icons = {
            dashboard: '<svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="3" y="3" width="7" height="7"/><rect x="14" y="3" width="7" height="7"/><rect x="14" y="14" width="7" height="7"/><rect x="3" y="14" width="7" height="7"/></svg>',
            users: '<svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path d="m16 21-5.5-9L16 3"/><path d="m21 21-5.5-9L21 3"/><path d="m6 21-5.5-9L6 3"/></svg>',
            courses: '<svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/></svg>',
            analytics: '<svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path d="M3 3v18h18"/><path d="m19 9-5 5-4-4-3 3"/></svg>',
            settings: '<svg class="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>'
        };
        return icons[iconName] || '';
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        this.isSidebarOpen = !this.isSidebarOpen;
        
        if (this.isSidebarOpen) {
            sidebar.classList.add('open');
            this.createOverlay();
        } else {
            sidebar.classList.remove('open');
            this.removeOverlay();
        }
    }

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('open');
        this.isSidebarOpen = false;
        this.removeOverlay();
    }

    createOverlay() {
        if (!document.getElementById('sidebar-overlay')) {
            const overlay = document.createElement('div');
            overlay.id = 'sidebar-overlay';
            overlay.className = 'fixed inset-0 bg-black/50 z-40';
            overlay.addEventListener('click', () => this.closeSidebar());
            document.body.appendChild(overlay);
        }
    }

    removeOverlay() {
        const overlay = document.getElementById('sidebar-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.lmsAdmin = new LMSAdmin();
});

// Handle window resize
window.addEventListener('resize', () => {
    if (window.lmsAdmin && window.innerWidth > 768) {
        window.lmsAdmin.closeSidebar();
    }
});